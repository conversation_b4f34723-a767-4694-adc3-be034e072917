#!/usr/bin/env python3
"""
调试权重文件尺寸不匹配问题
"""

import torch
from ultralytics import YOLO
import os

def check_model_info():
    """检查模型文件信息"""
    print("=== 检查模型文件信息 ===")
    
    # 检查 model_based_file.pt
    model_based_path = "./(f)models/model_based_file.pt"
    if os.path.exists(model_based_path):
        try:
            model_based = YOLO(model_based_path)
            print(f"✅ model_based_file.pt 加载成功")
            print(f"   类别数: {model_based.model.model[-1].nc}")
            print(f"   模型任务: {model_based.task}")
        except Exception as e:
            print(f"❌ model_based_file.pt 加载失败: {e}")
    else:
        print(f"❌ {model_based_path} 文件不存在")
    
    # 检查 yolov8-lite.yaml
    yaml_path = "yolov8-lite.yaml"
    if os.path.exists(yaml_path):
        try:
            model_yaml = YOLO(yaml_path)
            print(f"✅ yolov8-lite.yaml 加载成功")
            print(f"   类别数: {model_yaml.model.model[-1].nc}")
            print(f"   模型任务: {model_yaml.task}")
        except Exception as e:
            print(f"❌ yolov8-lite.yaml 加载失败: {e}")
    else:
        print(f"❌ {yaml_path} 文件不存在")

def check_weight_files():
    """检查权重文件"""
    print("\n=== 检查权重文件 ===")
    
    weight_files = [
        "./(f)models/yolov8_teacher_prepare-coco.pth",
        "./(f)models/yolov8_student_prepare-coco.pth"
    ]
    
    for weight_file in weight_files:
        if os.path.exists(weight_file):
            try:
                weights = torch.load(weight_file, map_location='cpu')
                print(f"✅ {os.path.basename(weight_file)} 加载成功")
                print(f"   权重键数量: {len(weights)}")
                
                # 查找分类层权重
                cls_layers = [k for k in weights.keys() if 'cv3' in k and 'weight' in k]
                if cls_layers:
                    for layer in cls_layers[:3]:  # 只显示前3个
                        print(f"   {layer}: {weights[layer].shape}")
                
            except Exception as e:
                print(f"❌ {os.path.basename(weight_file)} 加载失败: {e}")
        else:
            print(f"❌ {weight_file} 文件不存在")

def check_class_mismatch():
    """检查类别数不匹配问题"""
    print("\n=== 检查类别数匹配问题 ===")
    
    try:
        # 加载基础模型
        model_based = YOLO("./(f)models/model_based_file.pt")
        base_nc = model_based.model.model[-1].nc
        print(f"基础模型类别数: {base_nc}")
        
        # 加载YAML模型
        model_yaml = YOLO("yolov8-lite.yaml")
        yaml_nc = model_yaml.model.model[-1].nc
        print(f"YAML模型类别数: {yaml_nc}")
        
        if base_nc != yaml_nc:
            print(f"⚠️ 类别数不匹配! 基础模型:{base_nc} vs YAML模型:{yaml_nc}")
            return False
        else:
            print(f"✅ 类别数匹配: {base_nc}")
            return True
            
    except Exception as e:
        print(f"❌ 检查类别数时出错: {e}")
        return False

if __name__ == "__main__":
    check_model_info()
    check_weight_files()
    check_class_mismatch()
